<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocusGuard Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>

<body>
    <div class="popup-container">
        <!-- Header -->
        <header class="popup-header">
            <div class="logo">
                <div class="shield-icon">🛡️</div>
                <h1>FocusGuard Pro</h1>
            </div>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot active" id="statusDot"></span>
                <span class="status-text" id="statusText">Active</span>
            </div>
        </header>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3>Quick Actions</h3>

            <!-- Add Site Section -->
            <div class="action-section">
                <div class="input-group">
                    <input type="text" id="siteInput" placeholder="Enter website (e.g., facebook.com)"
                        class="site-input">
                    <button id="addSiteBtn" class="btn btn-primary">
                        <span class="btn-icon">+</span>
                        Block
                    </button>
                </div>
            </div>

            <!-- Current Site Section -->
            <div class="action-section" id="currentSiteSection">
                <div class="current-site">
                    <div class="site-info">
                        <div class="site-icon" id="currentSiteIcon">🌐</div>
                        <div class="site-details">
                            <h4 id="currentSiteName">Loading...</h4>
                            <p id="currentSiteStatus">Checking status...</p>
                        </div>
                    </div>
                    <button id="toggleCurrentSiteBtn" class="btn btn-secondary">
                        <span class="btn-icon" id="toggleCurrentSiteIcon">🚫</span>
                        <span id="toggleCurrentSiteText">Block</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Blocked Sites List -->
        <div class="blocked-sites">
            <div class="section-header">
                <h3>Blocked Sites</h3>
                <span class="site-count" id="siteCount">0 sites</span>
            </div>

            <div class="sites-list" id="sitesList">
                <div class="empty-state" id="emptyState">
                    <div class="empty-icon">🎯</div>
                    <p>No sites blocked yet</p>
                    <small>Add websites above to start focusing</small>
                </div>
            </div>
        </div>

        <!-- Footer Actions -->
        <div class="footer-actions">
            <button id="settingsBtn" class="btn btn-ghost">
                <span class="btn-icon">⚙️</span>
                Settings
            </button>
            <button id="focusModeBtn" class="btn btn-accent">
                <span class="btn-icon">🎯</span>
                Focus Mode
            </button>
        </div>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
            <p>Loading...</p>
        </div>
    </div>

    <script src="../utils/storage.js"></script>
    <script src="popup.js"></script>
</body>

</html>