<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocusGuard Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>

<body>
    <div class="popup-container">
        <!-- Header -->
        <header class="popup-header">
            <div class="logo">
                <div class="shield-icon">🛡️</div>
                <h1>FocusGuard Pro</h1>
            </div>
            <div class="header-controls">
                <button class="settings-btn" id="settingsBtn">⚙️</button>
            </div>
        </header>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Block Sites Tab -->
            <div class="tab-panel active" id="blockSitesPanel">
                <!-- Quick Actions -->
                <div class="quick-actions">
                    <!-- Add Site Section -->
                    <div class="action-section">
                        <div class="input-group">
                            <input type="text" id="siteInput" placeholder="Enter website (e.g., facebook.com)"
                                class="site-input">
                            <button id="addSiteBtn" class="btn btn-primary">
                                <span class="btn-icon">+</span>
                                Block
                            </button>
                        </div>
                    </div>

                    <!-- Current Site Section -->
                    <div class="action-section" id="currentSiteSection">
                        <div class="current-site">
                            <div class="site-info">
                                <div class="site-icon" id="currentSiteIcon">🌐</div>
                                <div class="site-details">
                                    <h4 id="currentSiteName">Loading...</h4>
                                    <p id="currentSiteStatus">Checking status...</p>
                                </div>
                            </div>
                            <button id="toggleCurrentSiteBtn" class="btn btn-secondary">
                                <span class="btn-icon" id="toggleCurrentSiteIcon">🚫</span>
                                <span id="toggleCurrentSiteText">Block</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Blocked Sites List -->
                <div class="blocked-sites">
                    <div class="section-header">
                        <h3>Blocked Sites</h3>
                        <span class="site-count" id="siteCount">0 sites</span>
                    </div>

                    <div class="sites-list" id="sitesList">
                        <div class="empty-state" id="emptyState">
                            <div class="empty-icon">🎯</div>
                            <p>No sites blocked yet</p>
                            <small>Add websites above to start focusing</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Focus Mode Tab -->
            <div class="tab-panel" id="focusModePanel">
                <div class="focus-content">
                    <!-- Session Status -->
                    <div class="session-status" id="sessionStatus">
                        <div class="session-info">
                            <span class="session-text" id="sessionText">1 of 2 cycles | Focus for 24 minutes</span>
                        </div>
                    </div>

                    <!-- Timer Section -->
                    <div class="timer-section">
                        <div class="timer-circle" id="timerCircle">
                            <div class="progress-ring" id="progressRing">
                                <svg width="200" height="200">
                                    <circle cx="100" cy="100" r="90" stroke="#e2e8f0" stroke-width="8" fill="none" />
                                    <circle cx="100" cy="100" r="90" stroke="#48bb78" stroke-width="8" fill="none"
                                        stroke-linecap="round" stroke-dasharray="565.48" stroke-dashoffset="565.48"
                                        transform="rotate(-90 100 100)" id="progressCircle" />
                                </svg>
                            </div>
                            <div class="timer-display">
                                <div class="time-text" id="timeText">25:00</div>
                                <div class="time-labels">
                                    <span>min</span>
                                    <span>sec</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Focus Status -->
                    <div class="focus-status">
                        <p id="focusStatusText">Focus session sites are blocked</p>
                    </div>

                    <!-- Controls -->
                    <div class="controls">
                        <button id="resetBtn" class="control-btn secondary">Reset</button>
                        <button id="startPauseBtn" class="control-btn primary">Start</button>
                    </div>
                </div>
            </div>

            <!-- Time Limits Tab -->
            <div class="tab-panel" id="timeLimitsPanel">
                <div class="time-limits-content">
                    <!-- Time Limits Header -->
                    <div class="limits-header">
                        <div class="limits-icon">⏰</div>
                        <h2>Set Time Limits</h2>
                        <p>Control your daily usage with smart time limits</p>
                    </div>

                    <!-- Enable Time Limits -->
                    <div class="setting-item">
                        <div class="setting-info">
                            <h4>Enable Time Limits</h4>
                            <p>Automatically block sites after daily limit</p>
                        </div>
                        <div class="toggle-switch" id="timeLimitsToggle"></div>
                    </div>

                    <!-- Default Daily Limit -->
                    <div class="input-group">
                        <label class="input-label">Default Daily Limit</label>
                        <select id="defaultDailyLimit" class="input-field">
                            <option value="30">30 minutes</option>
                            <option value="60">1 hour</option>
                            <option value="120" selected>2 hours</option>
                            <option value="180">3 hours</option>
                            <option value="0">No limit</option>
                        </select>
                    </div>

                    <!-- Quick Limit Buttons -->
                    <div class="quick-limits">
                        <h4>Quick Limits</h4>
                        <div class="limit-buttons">
                            <button class="limit-btn" data-limit="15">15m</button>
                            <button class="limit-btn" data-limit="30">30m</button>
                            <button class="limit-btn" data-limit="60">1h</button>
                            <button class="limit-btn" data-limit="120">2h</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <button class="nav-item active" data-tab="blockSitesPanel">
                <div class="nav-icon">🚫</div>
                <span>Block Sites</span>
            </button>
            <button class="nav-item" data-tab="focusModePanel">
                <div class="nav-icon">🎯</div>
                <span>Focus Mode</span>
            </button>
            <button class="nav-item" data-tab="timeLimitsPanel">
                <div class="nav-icon">⏰</div>
                <span>Time Limits</span>
            </button>
        </nav>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>

</html>