// FocusGuard Pro - Popup JavaScript
// Phase 1: Basic popup functionality

class FocusGuardPopup {
  constructor() {
    this.blockedSites = [];
    this.isEnabled = true;
    this.currentTab = null;
    this.activeTabPanel = 'blockSitesPanel';
    this.focusMode = null;
    this.setupGlobalMethods();
    this.init();
  }

  async init() {
    // Show loading
    this.showLoading(true);

    try {
      // Get current tab
      await this.getCurrentTab();

      // Load data
      await this.loadData();

      // Setup event listeners
      this.setupEventListeners();

      // Setup tab navigation
      this.setupTabNavigation();

      // Initialize focus mode
      this.initializeFocusMode();

      // Update UI
      this.updateUI();

      // Hide loading
      this.showLoading(false);
    } catch (error) {
      console.error('Popup initialization error:', error);
      this.showLoading(false);
    }
  }

  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
    } catch (error) {
      console.error('Error getting current tab:', error);
    }
  }

  async loadData() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getBlockedSites' });
      console.log('Popup received data:', response);
      if (response) {
        this.blockedSites = response.sites || [];
        this.isEnabled = response.isEnabled !== false;
        console.log('Popup loaded:', this.blockedSites.length, 'blocked sites');
      }
    } catch (error) {
      console.error('Error loading data:', error);
    }
  }

  setupEventListeners() {
    // Add site button
    const addSiteBtn = document.getElementById('addSiteBtn');
    addSiteBtn.addEventListener('click', () => this.addSite());

    // Site input enter key
    const siteInput = document.getElementById('siteInput');
    siteInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        this.addSite();
      }
    });

    // Toggle current site
    const toggleCurrentSiteBtn = document.getElementById('toggleCurrentSiteBtn');
    toggleCurrentSiteBtn.addEventListener('click', () => this.toggleCurrentSite());

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    settingsBtn.addEventListener('click', () => this.openSettings());

    // Focus mode controls
    const startPauseBtn = document.getElementById('startPauseBtn');
    const resetBtn = document.getElementById('resetBtn');

    if (startPauseBtn) {
      startPauseBtn.addEventListener('click', () => this.toggleFocusMode());
    }

    if (resetBtn) {
      resetBtn.addEventListener('click', () => this.resetFocusMode());
    }

    // Time limits controls
    const timeLimitsToggle = document.getElementById('timeLimitsToggle');

    if (timeLimitsToggle) {
      timeLimitsToggle.addEventListener('click', () => this.toggleTimeLimits());
    }
  }

  setupTabNavigation() {
    const navItems = document.querySelectorAll('.nav-item');

    navItems.forEach(item => {
      item.addEventListener('click', () => {
        const targetTab = item.dataset.tab;
        this.switchTab(targetTab);
      });
    });
  }

  switchTab(tabId) {
    // Hide all tab panels
    const panels = document.querySelectorAll('.tab-panel');
    panels.forEach(panel => panel.classList.remove('active'));

    // Show target panel
    const targetPanel = document.getElementById(tabId);
    if (targetPanel) {
      targetPanel.classList.add('active');
    }

    // Update nav items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.classList.remove('active');
      if (item.dataset.tab === tabId) {
        item.classList.add('active');
      }
    });

    this.activeTabPanel = tabId;

    // Update focus mode if switching to focus tab
    if (tabId === 'focusModePanel' && this.focusMode) {
      this.focusMode.updateDisplay();
    }
  }

  initializeFocusMode() {
    // Initialize inline focus mode functionality
    this.focusMode = {
      isActive: false,
      isPaused: false,
      currentTime: 25 * 60, // 25 minutes in seconds
      totalTime: 25 * 60,
      timer: null,

      updateDisplay: () => {
        const timeText = document.getElementById('timeText');
        const progressCircle = document.getElementById('progressCircle');
        const startPauseBtn = document.getElementById('startPauseBtn');
        const sessionText = document.getElementById('sessionText');
        const focusStatusText = document.getElementById('focusStatusText');

        if (timeText) {
          const minutes = Math.floor(this.focusMode.currentTime / 60);
          const seconds = this.focusMode.currentTime % 60;
          timeText.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        if (progressCircle) {
          const progress = (this.focusMode.totalTime - this.focusMode.currentTime) / this.focusMode.totalTime;
          const circumference = 2 * Math.PI * 65; // radius = 65
          const offset = circumference - (progress * circumference);
          progressCircle.style.strokeDashoffset = offset;
        }

        if (startPauseBtn) {
          if (this.focusMode.isActive) {
            startPauseBtn.textContent = this.focusMode.isPaused ? 'Resume' : 'Pause';
            startPauseBtn.classList.remove('primary');
            startPauseBtn.classList.add('secondary');
          } else {
            startPauseBtn.textContent = 'Start';
            startPauseBtn.classList.remove('secondary');
            startPauseBtn.classList.add('primary');
          }
        }

        if (sessionText) {
          sessionText.textContent = this.focusMode.isActive ?
            'Focus session in progress' :
            '1 of 2 cycles | Focus for 25 minutes';
        }

        if (focusStatusText) {
          focusStatusText.textContent = this.focusMode.isActive ?
            'Focus session sites are blocked' :
            'Ready to start focus session';
        }
      }
    };

    this.focusMode.updateDisplay();
  }

  updateUI() {
    this.updateStatus();
    this.updateCurrentSite();
    this.updateBlockedSitesList();
    this.loadTimeLimitsSettings();
    this.updateTimeLimitsTable();
  }

  updateStatus() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');

    if (this.isEnabled) {
      statusDot.classList.add('active');
      statusDot.classList.remove('inactive');
      statusText.textContent = 'Active';
    } else {
      statusDot.classList.remove('active');
      statusDot.classList.add('inactive');
      statusText.textContent = 'Inactive';
    }
  }

  updateCurrentSite() {
    if (!this.currentTab || !this.currentTab.url) {
      document.getElementById('currentSiteSection').style.display = 'none';
      return;
    }

    const url = this.currentTab.url;
    const domain = this.extractDomain(url);

    if (!domain || url.startsWith('chrome://') || url.startsWith('chrome-extension://')) {
      document.getElementById('currentSiteSection').style.display = 'none';
      return;
    }

    document.getElementById('currentSiteSection').style.display = 'block';

    const siteIcon = document.getElementById('currentSiteIcon');
    const siteName = document.getElementById('currentSiteName');
    const siteStatus = document.getElementById('currentSiteStatus');
    const toggleBtn = document.getElementById('toggleCurrentSiteBtn');
    const toggleIcon = document.getElementById('toggleCurrentSiteIcon');
    const toggleText = document.getElementById('toggleCurrentSiteText');

    siteName.textContent = domain;

    const isBlocked = this.isSiteBlocked(domain);

    if (isBlocked) {
      siteStatus.textContent = 'Currently blocked';
      siteStatus.style.color = '#c53030';
      toggleIcon.textContent = '✓';
      toggleText.textContent = 'Unblock';
      toggleBtn.className = 'btn btn-accent';
    } else {
      siteStatus.textContent = 'Not blocked';
      siteStatus.style.color = '#718096';
      toggleIcon.textContent = '🚫';
      toggleText.textContent = 'Block';
      toggleBtn.className = 'btn btn-secondary';
    }

    // Set site icon
    siteIcon.textContent = this.getSiteIcon(domain);
    siteIcon.style.background = this.getSiteColor(domain);
  }

  updateBlockedSitesList() {
    const sitesList = document.getElementById('sitesList');
    const siteCount = document.getElementById('siteCount');
    const emptyState = document.getElementById('emptyState');

    siteCount.textContent = `${this.blockedSites.length} site${this.blockedSites.length !== 1 ? 's' : ''}`;

    if (this.blockedSites.length === 0) {
      sitesList.innerHTML = '';
      sitesList.appendChild(emptyState);
      return;
    }

    sitesList.innerHTML = '';

    this.blockedSites.forEach(domain => {
      const siteItem = this.createSiteItem(domain);
      sitesList.appendChild(siteItem);
    });
  }

  createSiteItem(domain) {
    const item = document.createElement('div');
    item.className = 'site-item';

    item.innerHTML = `
      <div class="site-info">
        <div class="site-icon" style="background: ${this.getSiteColor(domain)};">
          ${this.getSiteIcon(domain)}
        </div>
        <div class="site-details">
          <h4>${domain}</h4>
          <p>Blocked site</p>
        </div>
      </div>
    `;

    return item;
  }

  // toggleBlocking method removed - protection toggle is now only available in options page

  async addSite() {
    const siteInput = document.getElementById('siteInput');
    const url = siteInput.value.trim();

    console.log('Adding site:', url);

    if (!url) {
      this.showNotification('Please enter a website URL', 'warning');
      return;
    }

    const domain = this.extractDomain(url) || url;
    console.log('Extracted domain:', domain);

    if (this.isSiteBlocked(domain)) {
      this.showNotification('Site is already blocked', 'warning');
      return;
    }

    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addBlockedSite',
        url: domain
      });

      console.log('Add site response:', response);

      if (response && response.success) {
        this.blockedSites.push(domain);
        siteInput.value = '';
        this.updateUI();
        this.showNotification(`${domain} has been blocked`, 'success');
      } else {
        this.showNotification('Failed to block site', 'error');
      }
    } catch (error) {
      console.error('Error adding site:', error);
      this.showNotification('Failed to block site', 'error');
    }

    this.showLoading(false);
  }



  async toggleCurrentSite() {
    if (!this.currentTab || !this.currentTab.url) return;

    const domain = this.extractDomain(this.currentTab.url);
    if (!domain) return;

    const isBlocked = this.isSiteBlocked(domain);

    if (isBlocked) {
      // Redirect to settings page for site removal
      chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
      window.close();
    } else {
      await this.addSiteByDomain(domain);
    }
  }

  async addSiteByDomain(domain) {
    this.showLoading(true);

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'addBlockedSite',
        url: domain
      });

      if (response && response.success) {
        this.blockedSites.push(domain);
        this.updateUI();
        this.showNotification(`${domain} has been blocked`, 'success');
      }
    } catch (error) {
      console.error('Error adding site:', error);
      this.showNotification('Failed to block site', 'error');
    }

    this.showLoading(false);
  }

  openSettings() {
    chrome.tabs.create({ url: chrome.runtime.getURL('options/options.html') });
    window.close();
  }

  toggleFocusMode() {
    if (!this.focusMode.isActive) {
      this.startFocusSession();
    } else {
      if (this.focusMode.isPaused) {
        this.resumeFocusSession();
      } else {
        this.pauseFocusSession();
      }
    }
  }

  startFocusSession() {
    this.focusMode.isActive = true;
    this.focusMode.isPaused = false;
    this.focusMode.currentTime = this.focusMode.totalTime;

    this.focusMode.timer = setInterval(() => {
      if (!this.focusMode.isPaused && this.focusMode.currentTime > 0) {
        this.focusMode.currentTime--;
        this.focusMode.updateDisplay();

        if (this.focusMode.currentTime === 0) {
          this.completeFocusSession();
        }
      }
    }, 1000);

    this.focusMode.updateDisplay();
    this.showNotification('Focus session started!', 'success');
  }

  pauseFocusSession() {
    this.focusMode.isPaused = true;
    this.focusMode.updateDisplay();
    this.showNotification('Focus session paused', 'info');
  }

  resumeFocusSession() {
    this.focusMode.isPaused = false;
    this.focusMode.updateDisplay();
    this.showNotification('Focus session resumed', 'success');
  }

  resetFocusMode() {
    if (this.focusMode.timer) {
      clearInterval(this.focusMode.timer);
      this.focusMode.timer = null;
    }

    this.focusMode.isActive = false;
    this.focusMode.isPaused = false;
    this.focusMode.currentTime = this.focusMode.totalTime;
    this.focusMode.updateDisplay();
    this.showNotification('Focus session reset', 'info');
  }

  completeFocusSession() {
    if (this.focusMode.timer) {
      clearInterval(this.focusMode.timer);
      this.focusMode.timer = null;
    }

    this.focusMode.isActive = false;
    this.focusMode.isPaused = false;
    this.focusMode.currentTime = this.focusMode.totalTime;
    this.focusMode.updateDisplay();
    this.showNotification('Focus session completed! Great work!', 'success');
  }

  async toggleTimeLimits() {
    const toggle = document.getElementById('timeLimitsToggle');
    const isEnabled = toggle.classList.contains('active');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'updateSettings',
        settings: { enableTimeLimits: !isEnabled }
      });

      if (response && response.success) {
        if (isEnabled) {
          toggle.classList.remove('active');
          this.showNotification('Time limits disabled', 'info');
        } else {
          toggle.classList.add('active');
          this.showNotification('Time limits enabled', 'success');
        }
      }
    } catch (error) {
      console.error('Error toggling time limits:', error);
      this.showNotification('Failed to update time limits', 'error');
    }
  }



  // Load settings for time limits tab
  async loadTimeLimitsSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
      if (response && response.settings) {
        const settings = response.settings;

        // Update time limits toggle
        const timeLimitsToggle = document.getElementById('timeLimitsToggle');
        if (timeLimitsToggle) {
          if (settings.enableTimeLimits) {
            timeLimitsToggle.classList.add('active');
          } else {
            timeLimitsToggle.classList.remove('active');
          }
        }
      }
    } catch (error) {
      console.error('Error loading time limits settings:', error);
    }
  }

  // Update time limits table with blocked sites
  async updateTimeLimitsTable() {
    const tableBody = document.getElementById('limitsTableBody');
    const emptyLimits = document.getElementById('emptyLimits');

    if (!tableBody) return;

    // Clear existing content
    tableBody.innerHTML = '';

    if (this.blockedSites.length === 0) {
      tableBody.appendChild(emptyLimits);
      return;
    }

    // Get usage data and limits
    let usageData = {};
    let limitsData = {};

    try {
      const response = await chrome.runtime.sendMessage({ action: 'getUsageStats' });
      if (response) {
        usageData = response.usage || {};
        limitsData = response.limits || {};
      }
    } catch (error) {
      console.error('Error getting usage stats:', error);
    }

    // Create rows for each blocked site
    this.blockedSites.forEach(domain => {
      const row = this.createTimeLimitRow(domain, usageData[domain] || 0, limitsData[domain] || 0);
      tableBody.appendChild(row);
    });
  }

  createTimeLimitRow(domain, usageMinutes, limitMinutes) {
    const row = document.createElement('div');
    row.className = 'site-limit-row';

    const siteIcon = this.getSiteIcon(domain);
    const siteColor = this.getSiteColor(domain);

    // Calculate usage percentage and status
    let usagePercent = 0;
    let usageStatus = 'normal';
    let usageText = 'No limit';

    if (limitMinutes > 0) {
      usagePercent = Math.min((usageMinutes / limitMinutes) * 100, 100);
      const remainingMinutes = Math.max(limitMinutes - usageMinutes, 0);

      if (usagePercent >= 100) {
        usageStatus = 'danger';
        usageText = 'Limit reached';
      } else if (usagePercent >= 80) {
        usageStatus = 'warning';
        usageText = `${remainingMinutes}m left`;
      } else {
        usageText = `${remainingMinutes}m left`;
      }
    }

    row.innerHTML = `
      <div class="site-info">
        <div class="site-icon" style="background-color: ${siteColor}">
          ${siteIcon}
        </div>
        <div class="site-details">
          <h4>${domain}</h4>
          <p>Website</p>
        </div>
      </div>
      <div class="limit-column">
        <select class="limit-select" data-domain="${domain}">
          <option value="0" ${limitMinutes === 0 ? 'selected' : ''}>No limit</option>
          <option value="15" ${limitMinutes === 15 ? 'selected' : ''}>15 minutes</option>
          <option value="30" ${limitMinutes === 30 ? 'selected' : ''}>30 minutes</option>
          <option value="60" ${limitMinutes === 60 ? 'selected' : ''}>1 hour</option>
          <option value="120" ${limitMinutes === 120 ? 'selected' : ''}>2 hours</option>
          <option value="180" ${limitMinutes === 180 ? 'selected' : ''}>3 hours</option>
        </select>
      </div>
      <div class="usage-status">
        <div class="usage-bar">
          <div class="usage-progress ${usageStatus}" style="width: ${usagePercent}%"></div>
        </div>
        <span class="usage-text ${usageStatus}">${usageText}</span>
      </div>
    `;

    // Add event listener for limit changes
    const select = row.querySelector('.limit-select');
    select.addEventListener('change', (e) => {
      this.updateSiteLimit(domain, parseInt(e.target.value));
    });

    return row;
  }

  async updateSiteLimit(domain, limitMinutes) {
    try {
      const response = await chrome.runtime.sendMessage({
        action: 'setTimeLimit',
        domain: domain,
        limitMinutes: limitMinutes
      });

      if (response && response.success) {
        const limitText = limitMinutes === 0 ? 'No limit' :
          limitMinutes < 60 ? `${limitMinutes} minutes` :
            `${Math.floor(limitMinutes / 60)} hour${limitMinutes >= 120 ? 's' : ''}`;

        this.showNotification(`${domain} limit set to ${limitText}`, 'success');

        // Refresh the table to show updated usage status
        setTimeout(() => {
          this.updateTimeLimitsTable();
        }, 500);
      }
    } catch (error) {
      console.error('Error updating site limit:', error);
      this.showNotification('Failed to update limit', 'error');
    }
  }

  // Make methods available globally
  setupGlobalMethods() {
    // Setup any global methods if needed
  }

  extractDomain(url) {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace(/^www\./, '');
    } catch (error) {
      return null;
    }
  }

  isSiteBlocked(domain) {
    return this.blockedSites.includes(domain);
  }

  getSiteIcon(domain) {
    const icons = {
      'facebook.com': 'f',
      'twitter.com': '𝕏',
      'youtube.com': '▶',
      'instagram.com': '📷',
      'reddit.com': '🤖',
      'tiktok.com': '🎵'
    };
    return icons[domain] || '🌐';
  }

  getSiteColor(domain) {
    const colors = {
      'facebook.com': '#1877F2',
      'twitter.com': '#1DA1F2',
      'youtube.com': '#FF0000',
      'instagram.com': '#E4405F',
      'reddit.com': '#FF4500',
      'tiktok.com': '#000000'
    };
    return colors[domain] || '#667eea';
  }

  showLoading(show) {
    const overlay = document.getElementById('loadingOverlay');
    if (show) {
      overlay.classList.add('show');
    } else {
      overlay.classList.remove('show');
    }
  }

  showNotification(message, type = 'info') {
    // Simple notification - could be enhanced with a proper notification system
    console.log(`${type.toUpperCase()}: ${message}`);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new FocusGuardPopup();
});
