/* FocusGuard Pro - Options Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    display: flex;
    min-height: calc(100vh - 40px);
}

.sidebar {
    width: 220px;
    background: #fafbfc;
    padding: 24px 0;
    border-right: 1px solid #eef2f6;
}

.logo {
    padding: 0 20px 24px;
    border-bottom: 1px solid #eef2f6;
    margin-bottom: 16px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 8px;
}

.shield-icon {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    color: #718096;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.15s ease;
    border-radius: 0 20px 20px 0;
    margin-right: 12px;
    cursor: pointer;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.08);
    color: #667eea;
    transform: translateX(2px);
}

.nav-link.active {
    background: rgba(102, 126, 234, 0.12);
    color: #667eea;
    border-right: none;
}

.nav-icon {
    width: 16px;
    height: 16px;
    opacity: 0.8;
}

.main-content {
    flex: 1;
    padding: 32px;
    overflow-y: auto;
    max-height: calc(100vh - 40px);
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.page-header {
    margin-bottom: 24px;
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 6px;
}

.page-subtitle {
    color: #718096;
    font-size: 15px;
}

.settings-section {
    background: white;
    border: 1px solid #edf2f7;
    border-radius: 8px;
    margin-bottom: 16px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.section-header {
    padding: 16px 20px;
    border-bottom: 1px solid #edf2f7;
    background: #fafbfc;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 2px;
}

.section-description {
    color: #718096;
    font-size: 13px;
}

.section-content {
    padding: 20px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f7fafc;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h4 {
    font-size: 14px;
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 2px;
}

.setting-info p {
    color: #718096;
    font-size: 13px;
}

.toggle-switch {
    position: relative;
    width: 44px;
    height: 22px;
    background: #e2e8f0;
    border-radius: 11px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.toggle-switch.active {
    background: #667eea;
}

.toggle-switch::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 18px;
    height: 18px;
    background: white;
    border-radius: 50%;
    transition: transform 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.toggle-switch.active::after {
    transform: translateX(22px);
}

.input-group {
    margin-bottom: 16px;
}

.input-label {
    display: block;
    font-size: 13px;
    font-weight: 500;
    color: #4a5568;
    margin-bottom: 6px;
}

.input-field {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 13px;
    transition: border-color 0.2s ease;
    background: #fafbfc;
}

.input-field:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.08);
    background: white;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #f7fafc;
    color: #718096;
    border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e1;
}

.btn-danger {
    background: #dc2626;
    color: white;
}

.btn-danger:hover {
    background: #b91c1c;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.stat-card {
    background: #fafbfc;
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #edf2f7;
    text-align: center;
}

.stat-number {
    font-size: 20px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
}

.stat-label {
    color: #718096;
    font-size: 12px;
    font-weight: 500;
}

.site-list {
    max-height: 300px;
    overflow-y: auto;
}

.site-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f7fafc;
}

.site-item:last-child {
    border-bottom: none;
}

.site-info {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.site-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: white;
    background: #667eea;
}

.site-details h5 {
    font-size: 13px;
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 1px;
}

.site-details p {
    font-size: 11px;
    color: #718096;
}

.remove-btn {
    background: #fed7d7;
    color: #c53030;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.remove-btn:hover {
    background: #feb2b2;
}

.time-badge {
    background: #f7fafc;
    color: #718096;
    padding: 3px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #e2e8f0;
}

.time-badge:hover {
    background: #fed7d7;
    color: #c53030;
    border-color: #fecaca;
    transform: translateY(-1px);
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #718096;
}

.empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
}

.empty-state p {
    font-size: 14px;
    margin-bottom: 4px;
}

.empty-state small {
    font-size: 12px;
    color: #a0aec0;
}

.action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.danger-zone {
    border-color: #fecaca;
    background: #fef2f2;
}

.danger-zone .section-header {
    background: #fef2f2;
    border-color: #fecaca;
}

.danger-zone .section-title {
    color: #dc2626;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.show {
    display: flex;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 12px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-overlay p {
    font-size: 14px;
    color: #718096;
}

/* Scrollbar Styling */
.site-list::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
    width: 6px;
}

.site-list::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
    background: #f7fafc;
}

.site-list::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

/* Keyword Management Styles */
.keyword-management {
    margin-top: 20px;
}

.keyword-input-container {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.keyword-input-container .input-field {
    flex: 1;
}

.keywords-display {
    margin-top: 20px;
}

.keywords-container {
    min-height: 60px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    background: #fafbfc;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
}

.keywords-container.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a0aec0;
    font-size: 13px;
    font-style: italic;
}

.keyword-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    background: #667eea;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    cursor: default;
}

.keyword-tag.default {
    background: #48bb78;
}

.keyword-tag.custom {
    background: #667eea;
}

.keyword-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keyword-remove {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    padding: 0;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: all 0.2s ease;
}

.keyword-remove:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.keyword-count {
    font-size: 12px;
    color: #718096;
    margin: 0;
}

.keyword-categories {
    margin-top: 20px;
}

.category-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.category-btn {
    padding: 10px 12px;
    text-align: left;
    font-size: 12px;
    transition: all 0.2s ease;
}

.category-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e1;
    transform: translateY(-1px);
}

.category-btn.added {
    background: #c6f6d5;
    border-color: #9ae6b4;
    color: #22543d;
}

/* Responsive Design */
@media (max-width: 768px) {
    .keyword-input-container {
        flex-direction: column;
        align-items: stretch;
    }

    .category-buttons {
        grid-template-columns: 1fr;
    }
}

.site-list::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Phase 5: Schedule Interface Styles */
.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

.schedule-day {
    background: #fafbfc;
    border: 1px solid #edf2f7;
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
}

.schedule-day:hover {
    border-color: #cbd5e1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.day-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.day-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.time-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.time-input {
    flex: 1;
    padding: 8px 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 13px;
    background: white;
    transition: border-color 0.2s ease;
}

.time-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.08);
}

.time-inputs span {
    font-size: 12px;
    color: #718096;
    font-weight: 500;
}

.schedule-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 16px;
    border-top: 1px solid #edf2f7;
}

.override-status {
    background: #fef5e7;
    border: 1px solid #f6e05e;
    border-radius: 8px;
    padding: 16px;
    margin-top: 16px;
}

.status-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.status-text {
    font-size: 14px;
    font-weight: 500;
    color: #744210;
}

.status-time {
    font-size: 18px;
    font-weight: 600;
    color: #c53030;
    font-family: 'Courier New', monospace;
}

.btn-warning {
    background: #ed8936;
    color: white;
}

.btn-warning:hover {
    background: #dd6b20;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* Smart Redirect Styles */
.redirect-categories {
    margin-bottom: 24px;
}

.category-item {
    background: #fafbfc;
    border: 1px solid #edf2f7;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
}

.category-item h5 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 12px;
}

.redirect-mapping {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.redirect-mapping span:first-child {
    font-size: 13px;
    color: #718096;
    background: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    flex: 1;
    min-width: 200px;
}

.arrow {
    font-size: 16px;
    color: #667eea;
    font-weight: bold;
}

.redirect-select {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 13px;
    background: white;
    transition: border-color 0.2s ease;
}

.redirect-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.08);
}

.custom-redirects {
    border-top: 1px solid #edf2f7;
    padding-top: 20px;
}

.custom-redirects h5 {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 16px;
}

.custom-redirect-item {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.custom-source,
.custom-target {
    flex: 1;
    min-width: 180px;
}

.custom-redirects-list {
    margin-top: 16px;
}

.custom-redirect-entry {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #fafbfc;
    border: 1px solid #edf2f7;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
}

.redirect-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.redirect-source {
    font-size: 13px;
    color: #2d3748;
    font-weight: 500;
}

.redirect-target {
    font-size: 13px;
    color: #667eea;
}

.remove-redirect-btn {
    background: #fed7d7;
    color: #c53030;
    border: none;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.remove-redirect-btn:hover {
    background: #feb2b2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        margin: 10px;
        min-height: calc(100vh - 20px);
    }

    .sidebar {
        width: 100%;
        padding: 16px 0;
    }

    .nav-menu {
        display: flex;
        overflow-x: auto;
        padding: 0 16px;
    }

    .nav-item {
        margin-bottom: 0;
        margin-right: 8px;
        flex-shrink: 0;
    }

    .nav-link {
        margin-right: 0;
        border-radius: 6px;
        white-space: nowrap;
    }

    .main-content {
        padding: 20px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
        flex-direction: column;
    }

    .schedule-grid {
        grid-template-columns: 1fr;
    }

    .redirect-mapping {
        flex-direction: column;
        align-items: stretch;
    }

    .redirect-mapping span:first-child,
    .redirect-select {
        min-width: auto;
    }

    .custom-redirect-item {
        flex-direction: column;
        align-items: stretch;
    }

    .custom-source,
    .custom-target {
        min-width: auto;
    }

    .schedule-actions {
        flex-direction: column;
    }

    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* Phrase management styles */
.phrase-management {
    margin-top: 20px;
}

.phrase-input-container {
    display: flex;
    gap: 8px;
    align-items: center;
}

.phrases-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    min-height: 40px;
    padding: 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #f8fafc;
    margin-top: 8px;
}

.phrases-container.empty {
    color: #718096;
    font-style: italic;
    align-items: center;
    justify-content: center;
}

.phrase-tag {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    background: #dbeafe;
    color: #1e40af;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.phrase-tag.user-defined {
    background: #dbeafe;
    color: #1e40af;
}

.phrase-remove {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    padding: 0;
    margin-left: 4px;
}

.phrase-remove:hover {
    color: #dc2626;
}

.phrase-count {
    font-size: 12px;
    color: #718096;
    margin-top: 8px;
}

/* Suggested phrases styles */
.suggested-phrases-container {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #f8fafc;
    margin-top: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.suggested-category {
    border-bottom: 1px solid #e2e8f0;
    padding: 16px;
}

.suggested-category:last-child {
    border-bottom: none;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.category-header h4 {
    margin: 0;
    color: #374151;
    font-size: 14px;
    font-weight: 600;
}

.add-category-btn {
    font-size: 12px;
    padding: 6px 12px;
}

.add-category-btn.added {
    background: #10b981;
    color: white;
    cursor: default;
}

.add-category-btn.added:hover {
    background: #10b981;
}

.category-phrases {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.suggested-phrase {
    display: inline-block;
    padding: 3px 6px;
    background: #f3f4f6;
    color: #6b7280;
    border-radius: 3px;
    font-size: 11px;
    border: 1px solid #e5e7eb;
}

/* Blocked domains styles */
.blocked-domains-container {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    background: #f8fafc;
    margin-top: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.blocked-domains-container.empty {
    color: #718096;
    font-style: italic;
    padding: 20px;
    text-align: center;
}

.domain-group {
    border-bottom: 1px solid #e2e8f0;
    padding: 16px;
}

.domain-group:last-child {
    border-bottom: none;
}

.group-title {
    margin: 0 0 12px 0;
    color: #374151;
    font-size: 14px;
    font-weight: 600;
}

.domain-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.domain-tag {
    display: inline-block;
    padding: 3px 8px;
    background: #fef2f2;
    color: #dc2626;
    border-radius: 4px;
    font-size: 11px;
    border: 1px solid #fecaca;
    font-family: monospace;
}

.domain-count {
    font-size: 12px;
    color: #718096;
    margin-top: 8px;
}