<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FocusGuard Pro - Settings</title>
    <link rel="stylesheet" href="options.css">
</head>

<body>
    <div class="container">
        <nav class="sidebar">
            <div class="logo">
                <h1>
                    <div class="shield-icon">🛡️</div>
                    FocusGuard Pro
                </h1>
            </div>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#overview" class="nav-link active" data-section="overview">
                        <span class="nav-icon">📊</span>
                        Overview
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#blocking" class="nav-link" data-section="blocking">
                        <span class="nav-icon">🚫</span>
                        Block List
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#limits" class="nav-link" data-section="limits">
                        <span class="nav-icon">⏱️</span>
                        Time Limits
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#focus" class="nav-link" data-section="focus">
                        <span class="nav-icon">🎯</span>
                        Focus Mode
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#schedule" class="nav-link" data-section="schedule">
                        <span class="nav-icon">📅</span>
                        Schedule
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#phrases" class="nav-link" data-section="phrases">
                        <span class="nav-icon">🔍</span>
                        Phrase Blocking
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#adult" class="nav-link" data-section="adult">
                        <span class="nav-icon">🔐</span>
                        Adult Content
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#password" class="nav-link" data-section="password">
                        <span class="nav-icon">🔑</span>
                        Password
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#analytics" class="nav-link" data-section="analytics">
                        <span class="nav-icon">📈</span>
                        Analytics
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#general" class="nav-link" data-section="general">
                        <span class="nav-icon">⚙️</span>
                        General
                    </a>
                </li>
            </ul>
        </nav>

        <main class="main-content">
            <!-- Overview Section -->
            <section id="overview" class="content-section active">
                <div class="page-header">
                    <h1 class="page-title">Overview</h1>
                    <p class="page-subtitle">Your productivity statistics at a glance</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Quick Stats</h2>
                        <p class="section-description">Your FocusGuard Pro activity summary</p>
                    </div>
                    <div class="section-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number" id="blockedSitesCount">0</div>
                                <div class="stat-label">Sites Blocked</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="blocksToday">0</div>
                                <div class="stat-label">Blocks Today</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="timeSaved">0h</div>
                                <div class="stat-label">Time Saved</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number" id="successRate">100%</div>
                                <div class="stat-label">Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Extension Status</h2>
                        <p class="section-description">Current protection status</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>FocusGuard Protection</h4>
                                <p>Enable or disable website blocking</p>
                            </div>
                            <div class="toggle-switch active" id="mainProtectionToggle"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Block List Section -->
            <section id="blocking" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Block List</h1>
                    <p class="page-subtitle">Manage websites you want to block</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Add Website</h2>
                        <p class="section-description">Block new websites to improve your focus</p>
                    </div>
                    <div class="section-content">
                        <div class="input-group">
                            <label class="input-label">Website URL</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="text" id="newSiteInput" class="input-field"
                                    placeholder="Enter website URL (e.g., facebook.com)">
                                <button id="addSiteBtn" class="btn btn-primary">Add Site</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Blocked Websites</h2>
                        <p class="section-description">Currently blocked websites</p>
                    </div>
                    <div class="section-content">
                        <div class="site-list" id="blockedSitesList">
                            <div class="empty-state" id="emptyBlockedSites">
                                <div class="empty-icon">🎯</div>
                                <p>No sites blocked yet</p>
                                <small>Add websites above to start focusing</small>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Time Limits Section -->
            <section id="limits" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Time Limits</h1>
                    <p class="page-subtitle">Set daily usage limits for websites</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Time Limits</h2>
                        <p class="section-description">Set daily usage limits for websites</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Time Limits</h4>
                                <p>Automatically block sites after daily limit is reached</p>
                            </div>
                            <div class="toggle-switch active" id="timeLimitsToggle"></div>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Default Daily Limit</label>
                            <select id="defaultDailyLimit" class="input-field">
                                <option>30 minutes</option>
                                <option>1 hour</option>
                                <option selected>2 hours</option>
                                <option>3 hours</option>
                                <option>No limit</option>
                            </select>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Focus Mode Section -->
            <section id="focus" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Focus Mode</h1>
                    <p class="page-subtitle">Configure your Pomodoro timer settings</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Focus Mode</h2>
                        <p class="section-description">Configure your Pomodoro timer settings</p>
                    </div>
                    <div class="section-content">
                        <div class="input-group">
                            <label class="input-label">Work Session Duration</label>
                            <select id="workSessionDuration" class="input-field">
                                <option>15 minutes</option>
                                <option>20 minutes</option>
                                <option selected>25 minutes</option>
                                <option>30 minutes</option>
                                <option>45 minutes</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Break Duration</label>
                            <select id="breakDuration" class="input-field">
                                <option selected>5 minutes</option>
                                <option>10 minutes</option>
                                <option>15 minutes</option>
                            </select>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Auto-start breaks</h4>
                                <p>Automatically start break timer when work session ends</p>
                            </div>
                            <div class="toggle-switch active" id="autoStartBreaksToggle"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Schedule Section -->
            <section id="schedule" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Schedule</h1>
                    <p class="page-subtitle">Set specific hours for site access</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Schedule Mode</h2>
                        <p class="section-description">Control when sites can be accessed</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Schedule Mode</h4>
                                <p>Block sites outside of scheduled hours</p>
                            </div>
                            <div class="toggle-switch" id="scheduleToggle"></div>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Schedule Type</label>
                            <select id="scheduleType" class="input-field">
                                <option value="daily" selected>Daily schedule</option>
                                <option value="weekly">Weekly schedule</option>
                                <option value="custom">Custom schedule</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Default Schedule Profile</label>
                            <select id="scheduleProfile" class="input-field">
                                <option value="work">Work Hours (9 AM - 5 PM)</option>
                                <option value="study">Study Hours (8 AM - 6 PM)</option>
                                <option value="evening">Evening Only (6 PM - 10 PM)</option>
                                <option value="custom" selected>Custom Profile</option>
                            </select>
                        </div>

                        <p style="color: #48bb78; font-size: 13px; margin-top: 16px;">
                            <strong>Active:</strong> Schedule configuration is now available.
                        </p>
                    </div>
                </div>

                <!-- Daily Schedule Configuration -->
                <div class="settings-section" id="dailyScheduleSection">
                    <div class="section-header">
                        <h2 class="section-title">Daily Schedule</h2>
                        <p class="section-description">Set allowed hours for each day</p>
                    </div>
                    <div class="section-content">
                        <div class="schedule-grid">
                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Monday</h4>
                                    <div class="toggle-switch active" id="mondayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="mondayStart" class="time-input" value="09:00">
                                    <span>to</span>
                                    <input type="time" id="mondayEnd" class="time-input" value="17:00">
                                </div>
                            </div>

                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Tuesday</h4>
                                    <div class="toggle-switch active" id="tuesdayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="tuesdayStart" class="time-input" value="09:00">
                                    <span>to</span>
                                    <input type="time" id="tuesdayEnd" class="time-input" value="17:00">
                                </div>
                            </div>

                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Wednesday</h4>
                                    <div class="toggle-switch active" id="wednesdayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="wednesdayStart" class="time-input" value="09:00">
                                    <span>to</span>
                                    <input type="time" id="wednesdayEnd" class="time-input" value="17:00">
                                </div>
                            </div>

                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Thursday</h4>
                                    <div class="toggle-switch active" id="thursdayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="thursdayStart" class="time-input" value="09:00">
                                    <span>to</span>
                                    <input type="time" id="thursdayEnd" class="time-input" value="17:00">
                                </div>
                            </div>

                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Friday</h4>
                                    <div class="toggle-switch active" id="fridayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="fridayStart" class="time-input" value="09:00">
                                    <span>to</span>
                                    <input type="time" id="fridayEnd" class="time-input" value="17:00">
                                </div>
                            </div>

                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Saturday</h4>
                                    <div class="toggle-switch" id="saturdayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="saturdayStart" class="time-input" value="10:00">
                                    <span>to</span>
                                    <input type="time" id="saturdayEnd" class="time-input" value="14:00">
                                </div>
                            </div>

                            <div class="schedule-day">
                                <div class="day-header">
                                    <h4>Sunday</h4>
                                    <div class="toggle-switch" id="sundayToggle"></div>
                                </div>
                                <div class="time-inputs">
                                    <input type="time" id="sundayStart" class="time-input" value="10:00">
                                    <span>to</span>
                                    <input type="time" id="sundayEnd" class="time-input" value="14:00">
                                </div>
                            </div>
                        </div>

                        <div class="schedule-actions">
                            <button id="applyToAllDaysBtn" class="btn btn-secondary">Apply to All Days</button>
                            <button id="saveScheduleBtn" class="btn btn-primary">Save Schedule</button>
                        </div>
                    </div>
                </div>

                <!-- Schedule Override -->
                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Schedule Override</h2>
                        <p class="section-description">Temporarily override schedule restrictions</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Emergency Override</h4>
                                <p>Temporarily disable schedule for urgent access</p>
                            </div>
                            <button id="emergencyOverrideBtn" class="btn btn-warning">Override (30 min)</button>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Override Duration</label>
                            <select id="overrideDuration" class="input-field">
                                <option value="15">15 minutes</option>
                                <option value="30" selected>30 minutes</option>
                                <option value="60">1 hour</option>
                                <option value="120">2 hours</option>
                            </select>
                        </div>

                        <div id="overrideStatus" class="override-status" style="display: none;">
                            <div class="status-info">
                                <span class="status-text">Schedule override active</span>
                                <span class="status-time" id="overrideTimeRemaining">30:00</span>
                            </div>
                            <button id="cancelOverrideBtn" class="btn btn-danger btn-small">Cancel</button>
                        </div>
                    </div>
                </div>

                <!-- Smart Redirect Configuration -->
                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Smart Site Redirect</h2>
                        <p class="section-description">Redirect blocked sites to productive alternatives</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Smart Redirects</h4>
                                <p>Automatically redirect to productive alternatives</p>
                            </div>
                            <div class="toggle-switch" id="smartRedirectToggle"></div>
                        </div>

                        <div class="redirect-categories">
                            <div class="category-item">
                                <h5>Social Media → Productivity</h5>
                                <div class="redirect-mapping">
                                    <span>facebook.com, twitter.com, instagram.com</span>
                                    <span class="arrow">→</span>
                                    <select class="redirect-select">
                                        <option value="todoist.com">Todoist (Task Management)</option>
                                        <option value="notion.so" selected>Notion (Notes & Planning)</option>
                                        <option value="calendar.google.com">Google Calendar</option>
                                        <option value="custom">Custom URL...</option>
                                    </select>
                                </div>
                            </div>

                            <div class="category-item">
                                <h5>Entertainment → Learning</h5>
                                <div class="redirect-mapping">
                                    <span>youtube.com, netflix.com, twitch.tv</span>
                                    <span class="arrow">→</span>
                                    <select class="redirect-select">
                                        <option value="coursera.org" selected>Coursera (Online Courses)</option>
                                        <option value="khanacademy.org">Khan Academy</option>
                                        <option value="duolingo.com">Duolingo (Language Learning)</option>
                                        <option value="custom">Custom URL...</option>
                                    </select>
                                </div>
                            </div>

                            <div class="category-item">
                                <h5>Shopping → Finance</h5>
                                <div class="redirect-mapping">
                                    <span>amazon.com, ebay.com, shopping sites</span>
                                    <span class="arrow">→</span>
                                    <select class="redirect-select">
                                        <option value="mint.com" selected>Mint (Budget Tracking)</option>
                                        <option value="ynab.com">YNAB (Budgeting)</option>
                                        <option value="personalcapital.com">Personal Capital</option>
                                        <option value="custom">Custom URL...</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="custom-redirects">
                            <h5>Custom Redirects</h5>
                            <div class="custom-redirect-item">
                                <input type="text" placeholder="Blocked site (e.g., reddit.com)"
                                    class="input-field custom-source">
                                <span class="arrow">→</span>
                                <input type="text" placeholder="Redirect to (e.g., news.ycombinator.com)"
                                    class="input-field custom-target">
                                <button class="btn btn-primary btn-small">Add</button>
                            </div>
                            <div id="customRedirectsList" class="custom-redirects-list">
                                <!-- Custom redirects will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Phrase Blocking Section -->
            <section id="phrases" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Phrase Blocking</h1>
                    <p class="page-subtitle">Block content containing specific words or phrases</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Phrase Blocking</h2>
                        <p class="section-description">Block content containing user-defined single words or multi-word
                            phrases</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Phrase Blocking</h4>
                                <p>Block content containing exact words or phrases you define</p>
                            </div>
                            <div class="toggle-switch active" id="phraseBlockingToggle"></div>
                        </div>

                        <div class="phrase-management">
                            <div class="input-group">
                                <label class="input-label">Add New Phrase (Single Words or Multi-Word Phrases)</label>
                                <div class="phrase-input-container">
                                    <input type="text" id="newPhraseInput" class="input-field"
                                        placeholder="Enter phrase (e.g., 'porn' or 'free porn videos')" maxlength="100">
                                    <button id="addPhraseBtn" class="btn btn-primary">Add</button>
                                </div>
                                <p style="color: #718096; font-size: 12px; margin-top: 4px;">
                                    ✓ Single words: High-precision blocking (e.g., "porn", "bdsm")<br>
                                    ✓ Multi-word phrases: Prevent false positives on legitimate sites
                                </p>
                            </div>

                            <div class="phrases-display">
                                <label class="input-label">Blocked Phrases</label>
                                <div class="phrases-container" id="phrasesContainer">
                                    <!-- Phrases will be dynamically loaded here -->
                                </div>
                                <p class="phrase-count" id="phraseCount">0 phrases blocked</p>
                            </div>

                            <div class="auto-phrases-info" style="margin-top: 24px;">
                                <label class="input-label">Research-Based Default Phrases</label>
                                <p style="color: #718096; font-size: 12px; margin-bottom: 12px;">
                                    ✅ <strong>Automatically Added:</strong> Research-based words and phrases have been
                                    automatically added to your blocked list. Includes both high-precision single words
                                    and multi-word phrases for comprehensive protection.
                                </p>
                                <div class="info-box"
                                    style="background: #f0fdf4; border: 1px solid #16a34a; border-radius: 6px; padding: 12px;">
                                    <p style="color: #15803d; margin: 0; font-size: 13px;">
                                        <strong>✨ Smart Auto-Setup:</strong> Your extension now includes 50+
                                        research-based terms like "porn", "bdsm", "porn video", "live sex cams" etc. -
                                        all automatically configured for optimal blocking.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="info-box"
                            style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 16px; margin-top: 16px;">
                            <h4 style="color: #0369a1; margin: 0 0 8px 0;">✨ Flexible Blocking System</h4>
                            <p style="color: #0369a1; margin: 0; font-size: 14px;">
                                This system supports both single words (high-precision blocking) and multi-word phrases
                                (false-positive prevention).
                                Choose the approach that works best for your needs.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Adult Content Section -->
            <section id="adult" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Adult Content Protection</h1>
                    <p class="page-subtitle">Automatically block inappropriate content</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Adult Content Protection</h2>
                        <p class="section-description">Automatically block inappropriate content</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Adult Content Blocking</h4>
                                <p>Block all known adult websites and content</p>
                            </div>
                            <div class="toggle-switch active" id="adultContentToggle"></div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Force Safe Search</h4>
                                <p>Automatically enable strict safe search on Google, Bing, Yahoo, and other search
                                    engines</p>
                            </div>
                            <div class="toggle-switch active" id="safeSearchToggle"></div>
                        </div>

                        <div class="blocked-domains-section" style="margin-top: 24px;">
                            <label class="input-label">Blocked Adult Content Domains</label>
                            <p style="color: #718096; font-size: 12px; margin-bottom: 12px;">
                                These domains are automatically blocked based on research data. Domain blocking prevents
                                access to explicit adult content sites.
                            </p>
                            <div class="blocked-domains-container" id="blockedDomainsContainer">
                                <!-- Blocked domains will be dynamically loaded here -->
                            </div>
                            <p class="domain-count" id="domainCount">0 domains blocked</p>
                        </div>

                        <p style="color: #48bb78; font-size: 13px; margin-top: 16px;">
                            <strong>Active:</strong> Adult content protection is now fully implemented and active.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Password Section -->
            <section id="password" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Master Password</h1>
                    <p class="page-subtitle">Secure your settings with a password</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Master Password</h2>
                        <p class="section-description">Secure your settings with a password</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Password Protection</h4>
                                <p>Require password to modify settings and unblock sites</p>
                            </div>
                            <div class="toggle-switch active" id="passwordProtectionToggle"></div>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Change Password</label>
                            <input type="password" id="masterPassword" class="input-field"
                                placeholder="Enter new password">
                        </div>

                        <button id="updatePasswordBtn" class="btn btn-primary">Update Password</button>

                        <p style="color: #718096; font-size: 13px; margin-top: 16px;">
                            <strong>Note:</strong> Password protection will be implemented in Phase 3.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">Analytics & Reports</h1>
                    <p class="page-subtitle">Track your productivity and usage patterns</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Analytics & Reports</h2>
                        <p class="section-description">Track your productivity and usage patterns</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Analytics</h4>
                                <p>Track time spent on websites and blocking statistics</p>
                            </div>
                            <div class="toggle-switch active" id="analyticsReportsToggle"></div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Weekly Reports</h4>
                                <p>Receive weekly productivity reports via email</p>
                            </div>
                            <div class="toggle-switch" id="weeklyReportsToggle"></div>
                        </div>

                        <button id="exportAnalyticsBtn" class="btn btn-secondary" style="margin-top: 16px;">Export
                            Data</button>

                        <p style="color: #718096; font-size: 13px; margin-top: 16px;">
                            <strong>Note:</strong> Advanced analytics will be implemented in Phase 7.
                        </p>
                    </div>
                </div>
            </section>

            <!-- General Settings Section -->
            <section id="general" class="content-section">
                <div class="page-header">
                    <h1 class="page-title">General Settings</h1>
                    <p class="page-subtitle">Configure general extension behavior</p>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Blocking Behavior</h2>
                        <p class="section-description">How FocusGuard should handle blocked sites</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Work in Incognito Mode</h4>
                                <p>Apply blocking rules in private browsing mode</p>
                            </div>
                            <div class="toggle-switch active" id="incognitoToggle"></div>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Show Notifications</h4>
                                <p>Display notifications for blocked sites and alerts</p>
                            </div>
                            <div class="toggle-switch active" id="notificationsToggle"></div>
                        </div>

                        <div class="input-group">
                            <label class="input-label">Default Block Duration</label>
                            <select id="defaultBlockDuration" class="input-field">
                                <option value="1hour">1 hour</option>
                                <option value="24hours">24 hours</option>
                                <option value="5days" selected>5 days</option>
                                <option value="1week">1 week</option>
                                <option value="permanent">Permanent</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <div class="section-header">
                        <h2 class="section-title">Data & Privacy</h2>
                        <p class="section-description">Manage your data and privacy settings</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Enable Analytics</h4>
                                <p>Track time spent on websites and blocking statistics</p>
                            </div>
                            <div class="toggle-switch active" id="analyticsToggle"></div>
                        </div>

                        <div class="action-buttons">
                            <button id="exportDataBtn" class="btn btn-secondary">Export Data</button>
                            <button id="importDataBtn" class="btn btn-secondary">Import Data</button>
                        </div>
                    </div>
                </div>

                <!-- Danger Zone -->
                <div class="settings-section danger-zone">
                    <div class="section-header">
                        <h2 class="section-title">Danger Zone</h2>
                        <p class="section-description">Irreversible and destructive actions</p>
                    </div>
                    <div class="section-content">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Reset All Settings</h4>
                                <p>This will reset all your settings to default values</p>
                            </div>
                            <button id="resetSettingsBtn" class="btn btn-secondary">Reset Settings</button>
                        </div>

                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Clear All Data</h4>
                                <p>Permanently delete all blocking data and statistics</p>
                            </div>
                            <button id="clearDataBtn" class="btn btn-danger">Clear Data</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Hidden file input for import -->
    <input type="file" id="importFileInput" accept=".json" style="display: none;">

    <!-- Loading overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>Loading...</p>
    </div>

    <script src="../utils/storage.js"></script>
    <script src="options.js"></script>
</body>

</html>