// FocusGuard Pro - Storage Utilities
// Centralized storage management for consistent data handling

class StorageManager {
  constructor() {
    this.storageKeys = {
      BLOCKED_SITES: 'blockedSites',
      IS_ENABLED: 'isEnabled',
      SETTINGS: 'settings',
      USAGE_STATS: 'usageStats',
      FOCUS_SESSIONS: 'focusSessions',
      TIME_LIMITS: 'timeLimits',
      MASTER_PASSWORD: 'masterPassword',
      BLOCKED_KEYWORDS: 'blockedKeywords'
    };
  }

  // Get data from storage
  async get(keys) {
    try {
      if (typeof keys === 'string') {
        keys = [keys];
      }
      return await chrome.storage.local.get(keys);
    } catch (error) {
      console.error('Storage get error:', error);
      return {};
    }
  }

  // Set data in storage
  async set(data) {
    try {
      await chrome.storage.local.set(data);
      return true;
    } catch (error) {
      console.error('Storage set error:', error);
      return false;
    }
  }

  // Remove data from storage
  async remove(keys) {
    try {
      if (typeof keys === 'string') {
        keys = [keys];
      }
      await chrome.storage.local.remove(keys);
      return true;
    } catch (error) {
      console.error('Storage remove error:', error);
      return false;
    }
  }

  // Clear all storage
  async clear() {
    try {
      await chrome.storage.local.clear();
      return true;
    } catch (error) {
      console.error('Storage clear error:', error);
      return false;
    }
  }

  // Get blocked sites
  async getBlockedSites() {
    const result = await this.get(this.storageKeys.BLOCKED_SITES);
    return result[this.storageKeys.BLOCKED_SITES] || [];
  }

  // Set blocked sites
  async setBlockedSites(sites) {
    return await this.set({
      [this.storageKeys.BLOCKED_SITES]: sites
    });
  }

  // Get extension enabled status
  async getIsEnabled() {
    const result = await this.get(this.storageKeys.IS_ENABLED);
    return result[this.storageKeys.IS_ENABLED] !== false; // Default to true
  }

  // Set extension enabled status
  async setIsEnabled(enabled) {
    return await this.set({
      [this.storageKeys.IS_ENABLED]: enabled
    });
  }

  // Get settings
  async getSettings() {
    const result = await this.get(this.storageKeys.SETTINGS);
    return result[this.storageKeys.SETTINGS] || this.getDefaultSettings();
  }

  // Set settings
  async setSettings(settings) {
    return await this.set({
      [this.storageKeys.SETTINGS]: settings
    });
  }

  // Get default settings
  getDefaultSettings() {
    return {
      defaultBlockDuration: '5days',
      showNotifications: true,
      workInIncognito: true,
      enableTimeTracking: true,
      enableAdultContentBlocking: true,
      enableKeywordBlocking: true,
      focusMode: {
        workDuration: 25, // minutes
        breakDuration: 5, // minutes
        autoStartBreaks: true
      },
      timeLimits: {
        enabled: false,
        defaultLimit: 120 // minutes
      }
    };
  }

  // Get usage statistics
  async getUsageStats() {
    const result = await this.get(this.storageKeys.USAGE_STATS);
    return result[this.storageKeys.USAGE_STATS] || {};
  }

  // Update usage statistics
  async updateUsageStats(domain, timeSpent) {
    const stats = await this.getUsageStats();
    const today = new Date().toDateString();

    if (!stats[today]) {
      stats[today] = {};
    }

    if (!stats[today][domain]) {
      stats[today][domain] = 0;
    }

    stats[today][domain] += timeSpent;

    return await this.set({
      [this.storageKeys.USAGE_STATS]: stats
    });
  }

  // Get focus sessions
  async getFocusSessions() {
    const result = await this.get(this.storageKeys.FOCUS_SESSIONS);
    return result[this.storageKeys.FOCUS_SESSIONS] || [];
  }

  // Add focus session
  async addFocusSession(session) {
    const sessions = await this.getFocusSessions();
    sessions.push({
      ...session,
      timestamp: Date.now()
    });

    return await this.set({
      [this.storageKeys.FOCUS_SESSIONS]: sessions
    });
  }

  // Get time limits
  async getTimeLimits() {
    const result = await this.get(this.storageKeys.TIME_LIMITS);
    return result[this.storageKeys.TIME_LIMITS] || {};
  }

  // Set time limit for domain
  async setTimeLimit(domain, limitMinutes) {
    const limits = await this.getTimeLimits();
    limits[domain] = limitMinutes;

    return await this.set({
      [this.storageKeys.TIME_LIMITS]: limits
    });
  }

  // Get blocked phrases (user-defined only, no defaults)
  async getBlockedPhrases() {
    const result = await this.get('blockedPhrases');
    return result.blockedPhrases || [];
  }

  // Set blocked phrases
  async setBlockedPhrases(phrases) {
    return await this.set({
      blockedPhrases: phrases
    });
  }

  // Export all data
  async exportData() {
    try {
      const allData = await chrome.storage.local.get(null);
      return {
        exportDate: new Date().toISOString(),
        version: '1.0.0',
        data: allData
      };
    } catch (error) {
      console.error('Export data error:', error);
      return null;
    }
  }

  // Import data
  async importData(importData) {
    try {
      if (!importData.data) {
        throw new Error('Invalid import data format');
      }

      await chrome.storage.local.clear();
      await chrome.storage.local.set(importData.data);
      return true;
    } catch (error) {
      console.error('Import data error:', error);
      return false;
    }
  }
}

// Create global instance
const storageManager = new StorageManager();

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StorageManager;
}
